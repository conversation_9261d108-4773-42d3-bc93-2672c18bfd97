Imports System.Globalization
Imports System.Collections.Generic
Imports System.Security.Cryptography
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Web
Imports System.Data
Imports System.Configuration

Module SPMJ_Mod

    '----CONFIG SERVER LIVE - PDSA----
    ' Public ServerId_Login As String = "Provider=SQLOLEDB; Data Source=" & ConfigurationManager.AppSettings("IP_App") & "; Initial Catalog=" & ConfigurationManager.AppSettings("dB") & "; User ID=ro; Password=DbSPMJ@pass"
    ' Public ServerId As String = "Provider=SQLOLEDB; Data Source=" & ConfigurationManager.AppSettings("IP_App") & "; Initial Catalog=" & ConfigurationManager.AppSettings("dB") & "; User ID=sa; Password=DbSPMJ@pass"
    ' Public ServerId_SQL As String = "Server=" & ConfigurationManager.AppSettings("IP_App") & "; DataBase=" & ConfigurationManager.AppSettings("dB") & "; UID=sa; PWD=DbSPMJ@pass"
    ' Public Pwd As String = "DbSPMJ@pass"
    ' Public NoKP As String
    ' Public Sektor As Integer
    ' Public NoKP_APC As String

    '----CONFIG SERVER LIVE----
    'Public ServerId_Login As String = "Provider=SQLOLEDB.1; Data Source=" & ConfigurationManager.AppSettings("IP_App") & "; Initial Catalog=" & ConfigurationManager.AppSettings("dB") & "; User ID=ro; Password=aljaroom5621"
    'Public ServerId As String = "Provider=SQLOLEDB.1; Data Source=" & ConfigurationManager.AppSettings("IP_App") & "; Initial Catalog=" & ConfigurationManager.AppSettings("dB") & "; User ID=sa; Password=aljaroom5621"
    'Public ServerId_SQL As String = "Server=" & ConfigurationManager.AppSettings("IP_App") & "; DataBase=" & ConfigurationManager.AppSettings("dB") & "; UID=sa; PWD=aljaroom5621"
    'Public Pwd As String = "aljaroom5621"
    'Public NoKP As String
    'Public Sektor As Integer
    'Public NoKP_APC As String


    ''----CONFIG SERVER LOCAL----
    Public ServerId_Login As String = "Provider=SQLOLEDB.1; Data Source=" & ConfigurationManager.AppSettings("IP_App") & "; Initial Catalog=" & ConfigurationManager.AppSettings("dB") & "; User ID=ro; Password=OhyHf1982"
    Public ServerId As String = "Provider=SQLOLEDB.1; Data Source=" & ConfigurationManager.AppSettings("IP_App") & "; Initial Catalog=" & ConfigurationManager.AppSettings("dB") & "; User ID=sa; Password=OhyHf1982"
    Public ServerId_SQL As String = "Server=" & ConfigurationManager.AppSettings("IP_App") & "; DataBase=" & ConfigurationManager.AppSettings("dB") & "; UID=sa; PWD=OhyHf1982"
    Public Pwd As String = "OhyHf1982"
    Public NoKP As String
    Public Sektor As Integer
    Public NoKP_APC As String

    Public Ex1 As String = "<script language='javascript'>var shell = new ActiveXObject('WScript.shell'); shell.run('"
    Public Ex2 As String = "', 1);</script>"
    ''Public s As String = "http://" & ConfigurationManager.AppSettings("IP_App2") & "/surat/"
    Public s As String = "https://" & ConfigurationManager.AppSettings("IP_App2") & "/spmj/surat/"

    Public Sub Msg(ByVal X As Page, ByVal Msg As String)
        X.ClientScript.RegisterClientScriptBlock(X.GetType(), "msgbox", "alert('" + Msg + "');", True)
    End Sub
    'Function Chk_Tkh(ByVal X As String)
    '    If X <> String.Empty Then X = "'" & X & "'" Else X = "NULL"
    '    Return X
    'End Function

    Function Chk_Tkh(ByVal X As String)
        Dim y As DateTime
        Dim z As String
        If X <> String.Empty Then
            y = DateTime.ParseExact(X, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z = y.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z = "'" & z & "'"
        Else
            z = "NULL"
        End If
        Return z
    End Function

    Function Fix_Date(ByVal X As String)
        Dim y As DateTime
        Dim z As String
        If X <> String.Empty Then
            y = DateTime.ParseExact(X, "MM/dd/yyyy", CultureInfo.InvariantCulture)
            z = y.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture)
            z = "'" & z & "'"
        Else
            z = "NULL"
        End If
        Return z
    End Function
    'Function Chk_Tkh(ByVal X As String)
    '    If X <> "" Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
    '    'If IsDate(X) Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
    '  Return X
    'End Function

    Function Fx(ByVal X As String)
        'Remove mofo blanks from grid
        If X = "&nbsp;" Then X = ""
        Return X
    End Function

    Public Sub Kira_Umur(ByVal X As String, ByVal Z As TextBox)
        If Len(X) > 6 Then
            If IsDate(Mid(X, 5, 2) & "/" & Mid(X, 3, 2) & "/" & Mid(X, 1, 2)) Then
                X = DateDiff(DateInterval.Year, CDate(Mid(X, 5, 2) & "/" & Mid(X, 3, 2) & "/" & Mid(X, 1, 2)), Now)
                Z.Text = X
            End If
        Else
            Z.Text = ""
        End If
    End Sub


    'Public Sub Kira_Umur2(ByVal X As Date, ByVal Z As TextBox)
    '    X = Format(X, "dd/MM/yyyy")
    '    Z.Text = ""
    '    Dim th, bl As Int16
    '    th = DateDiff(DateInterval.Year, X, Now)
    '    bl = DateDiff(DateInterval.Month, X, Now)
    '    If th = 0 Then
    '        Z.Text = ""
    '    Else
    '        Z.Text = th
    '    End If
    'End Sub

    'Comment Original 31102018 - OSH
    'Public Sub Kira_Umur2(ByVal X As DateTime, ByVal Z As TextBox)
    '    Dim y As Date = Date.Parse(X)
    '    Dim y2 As Date = Now
    '    Z.Text = DateDiff(DateInterval.Year, y, y2)
    'End Sub

    'Fix Covert Date 31102018  - OSH
    Public Sub Kira_Umur2(ByVal Y As String, ByVal Z As TextBox)
        Dim X As DateTime



        X = DateTime.ParseExact(Y, "dd/MM/yyyy", CultureInfo.InvariantCulture)
        Y = X.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)




        Dim umur As Integer
        umur = Today.Year - X.Year
        If (X > Today.AddYears(-umur)) Then umur -= 1
        Z.Text = umur
    End Sub
    'CHECK PERSONS IC TYPES RETURN ID TYPE VALUES 16102020 - OSH
    Function IC_TYPE(ByVal X As String)
        If X.ToString.Trim = "BARU" Then
            X = "0"
        ElseIf X.ToString.Trim = "TENTERA" Then
            X = "1"
        ElseIf X.ToString.Trim = "PASSPORT" Then
            X = "2"
        End If
        Return X
    End Function
    'Improve ACL 28092020 -OSH
    Function Akses_Pg(ByVal Modul As String, ByVal Form As String, ByVal Pg_Jenis As Int16, ByVal Pg_Modul As String)
        Akses_Pg = False
        Select Case Modul
            Case "PN"
                If Form = "Main" Then
                    If Pg_Jenis = 4 And Pg_Modul = 111111 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "Main2" Then
                    If Pg_Jenis = 4 And Pg_Modul = 111111 Then Akses_Pg = True : Exit Function 'Admin
                    If Pg_Jenis = 4 And Mid(Pg_Modul, 2, 1) = 1 Then Akses_Pg = True : Exit Function
                    If Pg_Jenis = 4 And Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
                End If
                If Form = "Exam" Then
                    'Fix Acesss Supervisor - Exam 19042021 - OSH
                    If Pg_Jenis = 2 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True : Exit Function
                    'Comment Original 28062021 - OSH
                    'If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                    'Fix LImit Access For Admin - Exam  28062021 - OSH
                    If Pg_Jenis = 4 And Pg_Modul = 111111 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "Institut" Then
                    'Fix Acesss Supervisor - Exam 19042021 - OSH
                    If Pg_Jenis = 2 Then
                        If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 Then
                        If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "Gred" Then
                    If Pg_Jenis = 4 Then
                        If Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "Tajaan" Then
                    If Pg_Jenis = 4 Then
                        If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "Majikan" Then
                    'Add Superrvisor Access 19042021 - OSH
                    If Pg_Jenis = 2 Then
                        If Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 Then
                        If Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "TPC" Then
                    If Pg_Jenis > 0 And Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "CPD" Then
                    If Pg_Jenis = 4 Then
                        If Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If
                If Form = "IC" Then
                    If Pg_Jenis = 4 Then
                        If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 5, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If

                'If Form = "Pengguna" Then
                '    If Pg_Jenis = 2 Then Akses_Pg = True
                'End If
                'If Form = "Lain" Then
                '    If Pg_Jenis > 0 Then Akses_Pg = True 'And Right(Pg_Modul, 1) = 1
                'End If
                'If Form = "Lain2" Then
                '    If Pg_Jenis >= 0 Then Akses_Pg = True 'And Right(Pg_Modul, 1) = 1
                'End If
                'If Form = "Siri" Then
                '    If Pg_Jenis = 2 Or (Pg_Jenis = 1 And Mid(Pg_Modul, 1, 1) = 1) Then Akses_Pg = True
                'End If
            Case "P1"
                If Form = "Pelatih_Daftar" Or Form = "Pelatih_Pinda" Or Form = "Pelatih_Saring" Then
                    If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
                End If
                If Form = "Jana_Calon" Or Form = "Semak_Calon" Then
                    If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
                End If
                If Form = "Isi_Markah" Then
                    If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
                End If
                If Form = "Laporan" Then
                    If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
                End If
                If Form = "Slip_No" Or Form = "Slip_Keputusan" Then
                    If Pg_Jenis = 4 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
                End If
                If Form = "Sah_Markah" Or Form = "Jana_Keputusan" Or Form = "Sah_Keputusan" Then
                    If Pg_Jenis >= 1 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
                End If
            Case "P2"
                If Form = "Slip" Or Form = "Foto" Then
                    If Pg_Jenis > 0 And Mid(Pg_Modul, 2, 1) = 1 Then Akses_Pg = True
                End If
            Case "P3"
                If Form = "Foto" Or Form = "Slip" Then
                    If Pg_Jenis > 0 And Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True
                End If
            Case "P4"
                If Form = "Slip" Then
                    If Pg_Jenis > 0 And Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True
                End If
                If Form = "Slip2" Then
                    If Pg_Jenis = 4 And Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True
                End If
                'Add Access Types -  Processor 16082018 - OSH
                If Form = "Proses" Then
                    If Pg_Jenis = 1 Then 'Pemproses
                        If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 4, 1) = 1 Or Mid(Pg_Modul, 5, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If
                'Add Access Types -  Registration 16082018 - OSH
                If Form = "Daftar" Then
                    If Pg_Jenis = 3 Then
                        If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 5, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
                End If

                'Add Access Types -  Processor 16082018 - OSH
                If Form = "Batal" Then
                    If Pg_Jenis = 2 Then 'peyelia
                        If Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True : Exit Function
                    End If
                    If Pg_Jenis = 4 And Pg_Modul = 111111 Then Akses_Pg = True : Exit Function 'Admin
                End If
        End Select
        Return Akses_Pg
    End Function

    'Comment Original 28092020 - OSH
    'Function Akses_Pg(ByVal Modul As String, ByVal Form As String, ByVal Pg_Jenis As Int16, ByVal Pg_Modul As String)
    '    Akses_Pg = False
    '    Select Case Modul
    '        Case "PN"
    '            If Form = "Main" Then
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "Main2" Then
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 Then Akses_Pg = True : Exit Function 'Admin
    '                If Pg_Jenis = 2 And Mid(Pg_Modul, 2, 1) = 1 Then Akses_Pg = True : Exit Function
    '                If Pg_Jenis = 2 And Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
    '            End If
    '            If Form = "Exam" Then
    '                If Pg_Jenis = 2 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True : Exit Function
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "Institut" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "Gred" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "Tajaan" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "Majikan" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "TPC" Then
    '                If Pg_Jenis > 0 And Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True : Exit Function
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "CPD" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            If Form = "IC" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 5, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If

    '            'If Form = "Pengguna" Then
    '            '    If Pg_Jenis = 2 Then Akses_Pg = True
    '            'End If
    '            'If Form = "Lain" Then
    '            '    If Pg_Jenis > 0 Then Akses_Pg = True 'And Right(Pg_Modul, 1) = 1
    '            'End If
    '            'If Form = "Lain2" Then
    '            '    If Pg_Jenis >= 0 Then Akses_Pg = True 'And Right(Pg_Modul, 1) = 1
    '            'End If
    '            'If Form = "Siri" Then
    '            '    If Pg_Jenis = 2 Or (Pg_Jenis = 1 And Mid(Pg_Modul, 1, 1) = 1) Then Akses_Pg = True
    '            'End If
    '        Case "P1"
    '            If Form = "Pelatih_Daftar" Or Form = "Pelatih_Pinda" Or Form = "Pelatih_Saring" Then
    '                If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
    '            End If
    '            If Form = "Jana_Calon" Or Form = "Semak_Calon" Then
    '                If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
    '            End If
    '            If Form = "Isi_Markah" Then
    '                If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
    '            End If
    '            If Form = "Laporan" Then
    '                If Pg_Jenis >= 0 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
    '            End If
    '            If Form = "Slip_No" Or Form = "Slip_Keputusan" Then
    '                If Pg_Jenis = 2 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
    '            End If
    '            If Form = "Sah_Markah" Or Form = "Jana_Keputusan" Or Form = "Sah_Keputusan" Then
    '                If Pg_Jenis >= 1 And Mid(Pg_Modul, 1, 1) = 1 Then Akses_Pg = True
    '            End If
    '        Case "P2"
    '            If Form = "Slip" Or Form = "Foto" Then
    '                If Pg_Jenis > 0 And Mid(Pg_Modul, 2, 1) = 1 Then Akses_Pg = True
    '            End If
    '        Case "P3"
    '            If Form = "Foto" Or Form = "Slip" Then
    '                If Pg_Jenis > 0 And Mid(Pg_Modul, 4, 1) = 1 Then Akses_Pg = True
    '            End If
    '        Case "P4"
    '            If Form = "Slip" Then
    '                If Pg_Jenis > 0 And Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True
    '            End If
    '            If Form = "Slip2" Then
    '                If Pg_Jenis = 2 And Mid(Pg_Modul, 3, 1) = 1 Then Akses_Pg = True
    '            End If
    '            'Add Access Types -  Processor 16082018 - OSH
    '            If Form = "Proses" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 5, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '            'Add Access Types -  Registration 16082018 - OSH
    '            If Form = "Daftar" Then
    '                If Pg_Jenis = 2 Then
    '                    If Mid(Pg_Modul, 1, 1) = 1 Or Mid(Pg_Modul, 2, 1) = 1 Or Mid(Pg_Modul, 3, 1) = 1 Or Mid(Pg_Modul, 5, 1) = 1 Then Akses_Pg = True : Exit Function
    '                End If
    '                If Pg_Jenis = 2 And Pg_Modul = 111111 = 1 Then Akses_Pg = True : Exit Function 'Admin
    '            End If
    '    End Select
    '    Return Akses_Pg
    'End Function

    Public Sub Kira_Umur3(ByVal X As Date, ByVal Z As TextBox)
        Z.Text = ""
        Z.Text = DateDiff(DateInterval.Year, X, Now)
    End Sub

    'Public Sub UserMsgBox(ByVal sMsg As String)

    '    Dim sb As New StringBuilder()
    '    Dim oFormObject As System.Web.UI.Control

    '    sMsg = sMsg.Replace("'", "\'")
    '    sMsg = sMsg.Replace(Chr(34), "\" & Chr(34))
    '    sMsg = sMsg.Replace(vbCrLf, "\n")
    '    sMsg = "<script language=javascript>alert(""" & sMsg & """)</script>"

    '    sb = New StringBuilder()
    '    sb.Append(sMsg)

    '    For Each oFormObject In Me.Controls
    '        If TypeOf oFormObject Is HtmlForm Then
    '            Exit For
    '        End If
    '    Next

    '    ' Add the javascript after the form object so that the
    '    ' message doesn't appear on a blank screen.
    '    oFormObject.Controls.AddAt(oFormObject.Controls.Count, New LiteralControl(sb.ToString()))

    'End Sub

    Function Chk_SQL(ByVal X As String)
        Chk_SQL = False : X = X.ToUpper
        ' Enhanced SQL injection protection
        If InStr(X, "--") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "/*") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "*/") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "OR'") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "OR ") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "OR(") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, " AND ") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, ";") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "UNION") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "SELECT") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "INSERT") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "UPDATE") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "DELETE") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "DROP") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "CREATE") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "ALTER") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "EXEC") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "EXECUTE") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "SP_") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "XP_") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "SCRIPT") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "<") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, ">") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "JAVASCRIPT") > 0 Then Chk_SQL = True : Exit Function
        If InStr(X, "VBSCRIPT") > 0 Then Chk_SQL = True : Exit Function
    End Function

    Function Apo(ByVal s As String) As String
        s = Replace(s, "'", "''")
        Return s
    End Function

    Function HyperLink(ByVal X As String)
        X = Replace(X, " ^", " <a href='")
        X = Replace(X, " | ", "'>")
        X = Replace(X, "^ ", "</a> ")
        Return X
    End Function

    ' Security Enhancement Functions
    Function HashPassword(ByVal password As String) As String
        ' Use SHA256 for password hashing with salt
        Dim salt As String = "SPMJ_SALT_2024_SECURE"
        Dim saltedPassword As String = password + salt

        Using sha256Hash As System.Security.Cryptography.SHA256 = System.Security.Cryptography.SHA256.Create()
            Dim bytes As Byte() = sha256Hash.ComputeHash(System.Text.Encoding.UTF8.GetBytes(saltedPassword))
            Dim builder As New System.Text.StringBuilder()
            For i As Integer = 0 To bytes.Length - 1
                builder.Append(bytes(i).ToString("x2"))
            Next
            Return builder.ToString()
        End Using
    End Function

    Function VerifyPassword(ByVal inputPassword As String, ByVal hashedPassword As String) As Boolean
        Dim hashedInput As String = HashPassword(inputPassword)
        Return hashedInput.Equals(hashedPassword, StringComparison.OrdinalIgnoreCase)
    End Function

    Function ValidatePasswordStrength(ByVal password As String) As Boolean
        ' Password must be at least 8 characters long
        If password.Length < 8 Then Return False

        ' Must contain at least one uppercase letter
        If Not System.Text.RegularExpressions.Regex.IsMatch(password, "[A-Z]") Then Return False

        ' Must contain at least one lowercase letter
        If Not System.Text.RegularExpressions.Regex.IsMatch(password, "[a-z]") Then Return False

        ' Must contain at least one digit
        If Not System.Text.RegularExpressions.Regex.IsMatch(password, "[0-9]") Then Return False

        Return True
    End Function

    Function SanitizeInput(ByVal input As String) As String
        If String.IsNullOrEmpty(input) Then Return String.Empty

        ' Remove potentially dangerous characters
        input = input.Replace("<", "&lt;")
        input = input.Replace(">", "&gt;")
        input = input.Replace("&", "&amp;")
        input = input.Replace("""", "&quot;")
        input = input.Replace("'", "&#39;")
        input = input.Trim()

        Return input
    End Function

    ' Login attempt tracking
    Private loginAttempts As New Dictionary(Of String, LoginAttemptInfo)

    Public Structure LoginAttemptInfo
        Public AttemptCount As Integer
        Public LastAttempt As DateTime
        Public IsLocked As Boolean
    End Structure

    Function IsAccountLocked(ByVal userId As String) As Boolean
        If loginAttempts.ContainsKey(userId) Then
            Dim attemptInfo As LoginAttemptInfo = loginAttempts(userId)
            If attemptInfo.IsLocked AndAlso DateTime.Now.Subtract(attemptInfo.LastAttempt).TotalMinutes < 30 Then
                Return True
            ElseIf DateTime.Now.Subtract(attemptInfo.LastAttempt).TotalMinutes >= 30 Then
                ' Reset after 30 minutes
                attemptInfo.AttemptCount = 0
                attemptInfo.IsLocked = False
                loginAttempts(userId) = attemptInfo
            End If
        End If
        Return False
    End Function

    Sub RecordLoginAttempt(ByVal userId As String, ByVal success As Boolean)
        Dim attemptInfo As LoginAttemptInfo

        If loginAttempts.ContainsKey(userId) Then
            attemptInfo = loginAttempts(userId)
        Else
            attemptInfo = New LoginAttemptInfo()
        End If

        attemptInfo.LastAttempt = DateTime.Now

        If success Then
            attemptInfo.AttemptCount = 0
            attemptInfo.IsLocked = False
        Else
            attemptInfo.AttemptCount += 1
            If attemptInfo.AttemptCount >= 5 Then
                attemptInfo.IsLocked = True
            End If
        End If

        loginAttempts(userId) = attemptInfo
    End Sub

    Function ImageLink(ByVal X As String)
        X = Replace(X, " *", " <img src='")
        X = Replace(X, "* ", "' height='100'> ")
        Return X
    End Function

    Sub Gred_PMR(ByVal x As DropDownList)
        x.Items.Clear()
        x.Items.Add("")
        x.Items.Add("A")
        x.Items.Add("B")
        x.Items.Add("C")
        x.Items.Add("D")
        x.Items.Add("E")
    End Sub

    Sub Gred_SPM(ByVal x As DropDownList)
        'x.Items.Clear()
        'x.Items.Add("")
        'x.Items.Add("1A")
        'x.Items.Add("2A")
        'x.Items.Add("3B")
        'x.Items.Add("4B")
        'x.Items.Add("5C")
        'x.Items.Add("6C")
        'x.Items.Add("7D")
        'x.Items.Add("8E")

        x.Items.Clear()
        x.Items.Add("")
        x.Items.Add("A+")
        x.Items.Add("1A\A")
        x.Items.Add("2A\A-")
        x.Items.Add("3B\B+")
        x.Items.Add("4B\B")
        x.Items.Add("5C\C+")
        x.Items.Add("6C\C")
        x.Items.Add("7D\D")
        x.Items.Add("8E\E")
    End Sub

    Sub Gred_SPM_Baru(ByVal x As DropDownList)
        x.Items.Clear()
        x.Items.Add("")
        x.Items.Add("A+")
        x.Items.Add("A")
        x.Items.Add("A-")
        x.Items.Add("B+")
        x.Items.Add("B")
        x.Items.Add("C+")
        x.Items.Add("C")
        x.Items.Add("D")
        x.Items.Add("E")
    End Sub

    Sub Gred_STPM(ByVal x As DropDownList)
        x.Items.Clear()
        x.Items.Add("")
        x.Items.Add("A")
        x.Items.Item(1).Value = 4.0
        x.Items.Add("A-")
        x.Items.Item(2).Value = 3.67
        x.Items.Add("B+")
        x.Items.Item(3).Value = 3.33
        x.Items.Add("B")
        x.Items.Item(4).Value = 3.0
        x.Items.Add("B-")
        x.Items.Item(5).Value = 2.67
        x.Items.Add("C+")
        x.Items.Item(6).Value = 2.33
        x.Items.Add("C")
        x.Items.Item(7).Value = 2.0
        x.Items.Add("C-")
        x.Items.Item(8).Value = 1.67
        x.Items.Add("D+")
        x.Items.Item(9).Value = 1.33
        x.Items.Add("D")
        x.Items.Item(10).Value = 1.0
        x.Items.Add("D-")
        x.Items.Item(11).Value = 0.67
    End Sub

    'Add O & A Level Grade 29072019 - OSH
    Sub Gred_O_A_Level(ByVal x As DropDownList)
        x.Items.Clear()
        x.Items.Add("")
        x.Items.Add("A*")
        x.Items.Add("A")
        x.Items.Add("B")
        x.Items.Add("C")
        x.Items.Add("D")
        x.Items.Add("E")
    End Sub
    'Add IGCSE GRADING 24082021 - OSH
    Sub Gred_IGCSE(ByVal x As DropDownList)
        x.Items.Clear()
        x.Items.Add("")
        x.Items.Add("A*")
        x.Items.Add("A")
        x.Items.Add("B")
        x.Items.Add("C")
        x.Items.Add("D")
        x.Items.Add("E")
        x.Items.Add("F")
        x.Items.Add("G")
    End Sub

    Function Header_Surat(ByVal Z As Int16, ByVal Y As Int16)
        Dim x As String = ""
        x = "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word'>"
        x += "<head><style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
        x += "<style type='text/css'><!-- .indent_3x { padding-left: 400pt;padding-right: 50pt;} --></style>"
        x += "<style> "
        x += "@page Section1 {mso-footer:f1;}"
        x += "div.Section1{page:Section1;}"
        x += "p.MsoFooter, li.MsoFooter, div.MsoFooter{"
        x += "mso-pagination:widow-orphan;"
        x += "tab-stops:center 216.0pt right 432.0pt;}"
        x += "</style>"
        x += "</head><body><div class='Section1'>"

        'Header

        x += "<table width='100%' style='border: none;mso-border-bottom-alt:solid windowtext .5pt;margin-left: 0px; font-family: Arial; font-size: 10pt;'><tr>"
        x += "<td valign='top'><img width=110 height=88 src='" & s & "jata.png'></img></td>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 10pt; color:#151B8D;'>"
        x += "<b>KEMENTERIAN KESIHATAN MALAYSIA</b>"
        If Y = 1 Then
            x += "<br/><i>MINISTRY OF HEALTH MALAYSIA</i>"
        End If
        If Z = 1 Then
            x += "<br/><b>LEMBAGA JURURAWAT MALAYSIA</b>"
            If Y = 1 Then
                x += "<br/><i>NURSING BOARD MALAYSIA</i>"
            End If
        Else
            x += "<br/><b>LEMBAGA BIDAN MALAYSIA</b>"
            If Y = 1 Then
                x += "<br/><i>MIDWIVES BOARD MALAYSIA</i>"
            End If
        End If
        x += "<br/><b>Aras 3, Blok E1, Kompleks E, Presint 1</b>"
        If Y = 1 Then
            x += "<br/><i>Level 3, Block E1, Complex E, Precinct 1</i>"
        End If
        x += "<br/><b>Pusat Pentadbiran Kerajaan Persekutuan</b>"
        If Y = 1 Then
            x += "<br/><i>Federal Government Administrative Centre</i>"
        End If
        x += "<br/><b>62590 Putrajaya</b></p></div></td>"
        x += "<td valign='top' style='width:21.64%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt; color:#151B8D;'>"
        If Z = 1 Then
            x += "<div align ='center'><img width=80 height=100 src='" & s & "ljm2.gif'></img>"
        Else
            x += "<div align ='center'><img width=100 height=100 src='" & s & "bidan.jpg'></img>"
        End If
        'x += "<v:shape ><v:imagedata src='" & s & "ljm2.gif'/>"
        x += "<br/>TEL :603-88833885/6/7/8"
        x += "<br/>FAX :603-88831329&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>"
        x += "</td>"
        x += "</tr></table>"

        Header_Surat = x
        ' <div align="center"></div>
    End Function

    Function Footer_Surat()
        Dim x As String = ""
        x = "<br clear=all style='page-break-before:always'>"
        'x += "<br clear=all style='page-break-before:always'>"
        x += "<div style='mso-element:footer' id='f1'>"
        'x += "<p class='MsoFooter'>"
        'x += "<span><img src='" & s & "img1.png'><img src='" & s & "img2.png'><img src='" & s & "img3.jpg'><img src='" & s & "img4.png'></span></p>"
        x += "<p class=MsoFooter style='border:none;mso-border-top-alt:solid windowtext 0.5pt;"
        x += "padding:0in;mso-padding-alt:0in 0in 1.0pt 0in'>"
        x += "<span style='mso-no-proof:yes'>"

        x += " <!--[if gte vml 1]>"
        x += "<v:shape id='_x0000_s2051' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:300.6pt;"
        x += "margin-top:5.75pt;width:68.2pt;height:33.8pt;z-index:-2' wrapcoords='-257 0 -257 21300 21600 21300 21600 0 -257 0'>"
        x += " <v:imagedata src='" & s & "imga3.png' o:title=''/>"
        x += "</v:shape><![endif]-->"

        x += "<!--[if gte vml 1]>"
        x += "<v:shape id='_x0000_s2049' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:100.2pt;"
        x += " margin-top:5.75pt;width:68.2pt;height:33.2pt;z-index:-4' wrapcoords='-237 0 -237 21375 21600 21375 21600 0 -237 0'"
        x += " o:allowoverlap='f'>"
        x += " <v:imagedata src='" & s & "imga1.png' o:title=''/>"
        x += "</v:shape><![endif]-->"

        x += "<!--[if gte vml 1]><v:shape id='_x0000_s2050' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:200.4pt;"
        x += " margin-top:5.75pt;width:68.2pt;height:32.6pt;z-index:-3' wrapcoords='-304 0 -304 21375 21600 21375 21600 0 -304 0'>"
        x += " <v:imagedata src='" & s & "imga2.png' o:title=''/>"
        x += "</v:shape><![endif]-->"

        'x += "<!--[if gte vml 1]><v:shape id='_x0000_s2052' type='#_x0000_t75'"
        'x += " style='position:absolute;left:0;text-align:left;margin-left:97.8pt;"
        'x += " margin-top:7.35pt;width:60pt;height:44.2pt;z-index:-1' wrapcoords='-225 0 -225 21373 21600 21373 21600 0 -225 0'"
        'x += " o:allowoverlap='f'>"
        'x += " <v:imagedata src='" & s & "imga1.png' o:title=''/>"
        'x += "</v:shape><![endif]-->"

        x += "</span></p>"
        x += "</div>"
        x += "</div>"
        x += "</body>"
        x += "</html>"
        Footer_Surat = x
    End Function

    Function Header_Surat_Blank(ByVal Z As Int16)
        Dim x As String = ""
        x = "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word'>"
        x += "<head><style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
        x += "<style type='text/css'><!-- .indent_3x { padding-left: 400pt;padding-right: 50pt;} --></style>"
        x += "<style> "
        x += "@page Section1 {mso-footer:f1;}"
        x += "div.Section1{page:Section1;}"
        x += "p.MsoFooter, li.MsoFooter, div.MsoFooter{"
        x += "mso-pagination:widow-orphan;"
        x += "tab-stops:center 216.0pt right 432.0pt;}"
        x += "</style>"
        x += "</head><body><div class='Section1'>"

        Header_Surat_Blank = x
    End Function

    Function Footer_Surat_Blank()
        Dim x As String = ""
        ''X += "<br clear=all style='page-break-before:always'>"
        'x += "<br clear=all style='page-break-before:always'>"
        'x += "<div style='mso-element:footer' id='f1'>"
        ''x += "<p class='MsoFooter'>"
        ''x += "<span><img src='" & s & "img1.png'><img src='" & s & "img2.png'><img src='" & s & "img3.jpg'><img src='" & s & "img4.png'></span></p>"
        'x += "<p class=MsoFooter style='border:none;mso-border-top-alt:solid windowtext 0.5pt;"
        'x += "padding:0in;mso-padding-alt:0in 0in 1.0pt 0in'><span style='mso-no-proof:yes'>"
        'x += " <!--[if gte vml 1]>"
        'x += "<v:shape style='position:absolute;left:0;text-align:left;margin-left:315pt;margin-top:3.75pt;width:48.6pt;"
        'x += " height:44.2pt;z-index:-2' wrapcoords='-257 0 -257 21300 21600 21300 21600 0 -257 0'>"
        'x += " <v:imagedata src='" & s & "imgd.png' o:title=''/>"
        'x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2049' type='#_x0000_t75'"
        'x += " style='position:absolute;left:0;text-align:left;margin-left:178.2pt;"
        'x += " margin-top:5.75pt;width:55.2pt;height:44.2pt;z-index:-4' wrapcoords='-237 0 -237 21375 21600 21375 21600 0 -237 0'"
        'x += " o:allowoverlap='f'>"
        'x += " <v:imagedata src='" & s & "imgb.png' o:title=''/>"
        'x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2050' type='#_x0000_t75'"
        'x += " style='position:absolute;left:0;text-align:left;margin-left:251.4pt;"
        'x += " margin-top:7.35pt;width:49.2pt;height:40.6pt;z-index:-3' wrapcoords='-304 0 -304 21375 21600 21375 21600 0 -304 0'>"
        'x += " <v:imagedata src='" & s & "imgc.gif' o:title=''/>"
        'x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2052' type='#_x0000_t75'"
        'x += " style='position:absolute;left:0;text-align:left;margin-left:97.8pt;"
        'x += " margin-top:7.35pt;width:60pt;height:44.2pt;z-index:-1' wrapcoords='-225 0 -225 21373 21600 21373 21600 0 -225 0'"
        'x += " o:allowoverlap='f'>"
        'x += " <v:imagedata src='" & s & "imga.png' o:title=''/>"
        'x += "</v:shape><![endif]--></span></p>"
        'x += "</div>"
        'x += "</div>"
        x += "</body>"
        x += "</html>"
        Footer_Surat_Blank = x
    End Function

    Function Tarikh(ByVal T As Int16)
        Dim x, y, z As String
        x = Day(Now)
        y = Month(Now)
        z = Year(Now)
        If y = "1" Then If T = 1 Then y = "Januari" Else y = "January"
        If y = "2" Then If T = 1 Then y = "Februari" Else y = "February"
        If y = "3" Then If T = 1 Then y = "Mac" Else y = "March"
        If y = "4" Then If T = 1 Then y = "April" Else y = "April"
        If y = "5" Then If T = 1 Then y = "Mei" Else y = "May"
        If y = "6" Then If T = 1 Then y = "Jun" Else y = "June"
        If y = "7" Then If T = 1 Then y = "Julai" Else y = "July"
        If y = "8" Then If T = 1 Then y = "Ogos" Else y = "August"
        If y = "9" Then If T = 1 Then y = "September" Else y = "September"
        If y = "10" Then If T = 1 Then y = "Oktober" Else y = "October"
        If y = "11" Then If T = 1 Then y = "November" Else y = "November"
        If y = "12" Then If T = 1 Then y = "Disember" Else y = "December"
        Return x + " " + y + " " + z
    End Function

    Function Tukar_Warga(ByVal chk As CheckBox, ByVal cb As DropDownList, ByVal cb2 As DropDownList)
        Dim x As Integer
        If chk.Checked = False Then x = 1
        If chk.Checked = True Then x = 2
        If cb.SelectedIndex = 5 Then x = 3
        If cb2.SelectedItem.Text.Trim = "MALAYSIA" Then x = 0
        Return x
    End Function

    Function Kira_Umur_Tkh_Server(ByVal Tkh_Lahir As Object)
        Dim Tkh_Lahir_Profail As String = Tkh_Lahir
        Dim Tkh_Lahir_YMD As DateTime
        Dim Umur_Sebenar As Integer
        'Tkh_Lahir_YMD = DateTime.ParseExact(Tkh_Lahir_Profail.Replace("/", "-"), "yyyy-MM-dd", CultureInfo.InvariantCulture)
        Try
            Tkh_Lahir_YMD = DateTime.ParseExact(Tkh_Lahir_Profail, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            Tkh_Lahir_YMD = Tkh_Lahir_YMD.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            Dim Tkh_Semasa = Date.Now
            Umur_Sebenar = Math.Floor(Tkh_Semasa.Subtract(Tkh_Lahir_YMD).TotalDays / 365.25)
            Return Umur_Sebenar
        Catch ex As Exception
            Return Umur_Sebenar = 0
            'Dim userBirthDateText = "07/30/2010"
            'Dim userBirthDate = Date.ParseExact(userBirthDateText.Replace("/", "-"), "MM-dd-yyyy", Nothing)
            'Dim currentDate = Date.Now
            'Dim age = Math.Floor(currentDate.Subtract(userBirthDate).TotalDays / 365)
        End Try
    End Function
End Module
